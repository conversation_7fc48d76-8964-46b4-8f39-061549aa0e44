#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能文档问答系统 - 一键安装和启动脚本
自动检查依赖、安装包、启动服务
"""

import os
import sys
import subprocess
import logging
import time
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SystemSetup:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.requirements_file = self.project_root / "requirements.txt"
        
    def check_python_version(self):
        """检查Python版本"""
        logger.info("🐍 检查Python版本...")
        
        if sys.version_info < (3, 8):
            logger.error("❌ 需要Python 3.8或更高版本")
            logger.error(f"当前版本: {sys.version}")
            return False
        
        logger.info(f"✅ Python版本: {sys.version}")
        return True
    
    def check_pip(self):
        """检查pip是否可用"""
        logger.info("📦 检查pip...")
        
        try:
            result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"✅ pip版本: {result.stdout.strip()}")
                return True
            else:
                logger.error("❌ pip不可用")
                return False
        except Exception as e:
            logger.error(f"❌ pip检查失败: {e}")
            return False
    
    def install_requirements(self):
        """安装Python依赖包"""
        logger.info("📚 安装Python依赖包...")
        
        if not self.requirements_file.exists():
            logger.error(f"❌ 依赖文件不存在: {self.requirements_file}")
            return False
        
        try:
            # 升级pip
            logger.info("⬆️  升级pip...")
            subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                         check=True, capture_output=True)
            
            # 安装依赖
            logger.info("📦 安装项目依赖...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(self.requirements_file)
            ], capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                logger.info("✅ 依赖包安装成功")
                return True
            else:
                logger.error(f"❌ 依赖包安装失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("❌ 依赖包安装超时")
            return False
        except Exception as e:
            logger.error(f"❌ 依赖包安装异常: {e}")
            return False
    
    def check_system_dependencies(self):
        """检查系统依赖"""
        logger.info("🔧 检查系统依赖...")
        
        dependencies = {
            "Redis": self.check_redis,
            "Tesseract OCR": self.check_tesseract,
            "AI模型": self.check_ai_models
        }
        
        all_ok = True
        for name, check_func in dependencies.items():
            logger.info(f"  检查 {name}...")
            if check_func():
                logger.info(f"  ✅ {name} 可用")
            else:
                logger.warning(f"  ⚠️  {name} 不可用")
                all_ok = False
        
        return all_ok
    
    def check_redis(self):
        """检查Redis服务"""
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, db=0, socket_timeout=5)
            r.ping()
            return True
        except Exception:
            logger.warning("    Redis服务未启动，请手动启动: redis-server")
            return False
    
    def check_tesseract(self):
        """检查Tesseract OCR"""
        try:
            result = subprocess.run(["tesseract", "--version"], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except Exception:
            logger.warning("    Tesseract OCR未安装")
            logger.warning("    Ubuntu/Debian: sudo apt install tesseract-ocr tesseract-ocr-chi-sim")
            logger.warning("    Windows: 下载安装 https://github.com/UB-Mannheim/tesseract/wiki")
            return False
    
    def check_ai_models(self):
        """检查AI模型"""
        model_paths = [
            r"D:\model\BAAI\bge-m3",
            r"D:\model\BAAI\bge-reranker-v2-m3"
        ]
        
        all_exist = True
        for path in model_paths:
            if os.path.exists(path):
                logger.info(f"    ✅ 模型存在: {path}")
            else:
                logger.warning(f"    ⚠️  模型不存在: {path}")
                all_exist = False
        
        if not all_exist:
            logger.warning("    请下载BGE-M3和BGE-Reranker-v2-M3模型")
            logger.warning("    或修改config.py中的模型路径")
        
        return all_exist
    
    def create_directories(self):
        """创建必要的目录"""
        logger.info("📁 创建项目目录...")
        
        directories = ['uploads', 'logs']
        for dir_name in directories:
            dir_path = self.project_root / dir_name
            dir_path.mkdir(exist_ok=True)
            logger.info(f"  ✅ 目录: {dir_path}")
        
        return True
    
    def generate_test_pdf(self):
        """生成测试PDF文件"""
        logger.info("📄 生成测试PDF文件...")
        
        try:
            result = subprocess.run([
                sys.executable, "generate_test_pdf.py"
            ], cwd=self.project_root, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                logger.info("✅ 测试PDF文件生成成功")
                return True
            else:
                logger.warning(f"⚠️  测试PDF生成失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.warning(f"⚠️  测试PDF生成异常: {e}")
            return False
    
    def start_application(self):
        """启动应用程序"""
        logger.info("🚀 启动智能文档问答系统...")
        
        try:
            # 设置环境变量
            env = os.environ.copy()
            env['PYTHONPATH'] = str(self.project_root)
            
            # 启动应用
            logger.info("🌐 启动Web服务器...")
            logger.info("📍 访问地址: http://localhost:5000")
            logger.info("⏹️  按 Ctrl+C 停止服务")
            
            # 运行应用
            subprocess.run([
                sys.executable, "app.py"
            ], cwd=self.project_root, env=env)
            
        except KeyboardInterrupt:
            logger.info("\n👋 服务已停止")
        except Exception as e:
            logger.error(f"❌ 启动应用失败: {e}")
            return False
        
        return True
    
    def run_setup(self):
        """运行完整安装流程"""
        logger.info("🎯 智能文档问答系统 - 自动安装")
        logger.info("=" * 50)
        
        setup_steps = [
            ("检查Python版本", self.check_python_version),
            ("检查pip", self.check_pip),
            ("安装Python依赖", self.install_requirements),
            ("检查系统依赖", self.check_system_dependencies),
            ("创建项目目录", self.create_directories),
            ("生成测试文件", self.generate_test_pdf),
        ]
        
        # 执行安装步骤
        for step_name, step_func in setup_steps:
            logger.info(f"\n🔄 {step_name}...")
            try:
                if not step_func():
                    logger.error(f"❌ {step_name} 失败")
                    if step_name in ["检查Python版本", "检查pip", "安装Python依赖"]:
                        logger.error("💥 关键步骤失败，无法继续")
                        return False
                    else:
                        logger.warning("⚠️  非关键步骤失败，继续执行")
            except Exception as e:
                logger.error(f"❌ {step_name} 异常: {e}")
                return False
        
        logger.info("\n" + "=" * 50)
        logger.info("✅ 安装完成！")
        logger.info("=" * 50)
        
        # 询问是否立即启动
        try:
            response = input("\n🚀 是否立即启动系统？(y/n): ").strip().lower()
            if response in ['y', 'yes', '是', '']:
                return self.start_application()
            else:
                logger.info("💡 您可以稍后运行 'python app.py' 启动系统")
                return True
        except KeyboardInterrupt:
            logger.info("\n👋 安装完成，稍后启动")
            return True

def main():
    """主函数"""
    try:
        setup = SystemSetup()
        success = setup.run_setup()
        
        if success:
            logger.info("\n🎉 系统准备就绪！")
            logger.info("📖 使用说明:")
            logger.info("  1. 访问 http://localhost:5000")
            logger.info("  2. 上传PDF或TXT文件")
            logger.info("  3. 开始智能问答")
            logger.info("  4. 运行 'python test_complete_system.py' 进行完整测试")
        else:
            logger.error("💥 安装失败，请检查错误信息")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("\n👋 安装被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 安装程序异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
