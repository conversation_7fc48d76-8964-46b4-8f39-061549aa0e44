#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的应用程序测试脚本
用于验证RAG系统的基本功能
"""

import requests
import json
import time

def test_app_status():
    """测试应用程序是否正常运行"""
    try:
        response = requests.get('http://127.0.0.1:5000/', timeout=10)
        if response.status_code == 200:
            print("✅ 应用程序正常运行")
            return True
        else:
            print(f"❌ 应用程序响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到应用程序: {e}")
        return False

def test_file_upload():
    """测试文件上传功能"""
    try:
        # 准备测试文件
        files = {'file': ('test_document.txt', open('test_document.txt', 'rb'), 'text/plain')}
        
        # 上传文件
        response = requests.post('http://127.0.0.1:5000/upload', files=files, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 文件上传成功")
            print(f"   响应: {result}")
            return True
        else:
            print(f"❌ 文件上传失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 文件上传请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 文件上传过程中出错: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试RAG应用程序...")
    print()
    
    # 测试应用程序状态
    print("1. 测试应用程序状态...")
    if not test_app_status():
        print("应用程序未运行，请先启动应用程序")
        return
    
    print()
    
    # 测试文件上传
    print("2. 测试文件上传功能...")
    test_file_upload()
    
    print()
    print("🎉 测试完成！")

if __name__ == "__main__":
    main()
