#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整系统测试脚本
测试所有优化后的功能，包括PDF生成、文件处理、向量存储等
"""

import os
import sys
import time
import requests
import logging
import subprocess
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CompleteSystemTester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_pdf_file = None
        
    def generate_test_pdf(self):
        """生成测试PDF文件"""
        logger.info("🔧 生成测试PDF文件...")
        try:
            # 运行PDF生成脚本
            result = subprocess.run([
                sys.executable, "generate_test_pdf.py"
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                # 查找生成的PDF文件
                pdf_files = list(Path(".").glob("test_document_*.pdf"))
                if pdf_files:
                    self.test_pdf_file = str(pdf_files[0])
                    logger.info(f"✅ 测试PDF文件生成成功: {self.test_pdf_file}")
                    return True
                else:
                    logger.error("❌ 未找到生成的PDF文件")
                    return False
            else:
                logger.error(f"❌ PDF生成失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("❌ PDF生成超时")
            return False
        except Exception as e:
            logger.error(f"❌ PDF生成异常: {e}")
            return False
    
    def test_server_connection(self):
        """测试服务器连接"""
        logger.info("🔗 测试服务器连接...")
        try:
            response = self.session.get(f"{self.base_url}/", timeout=10)
            if response.status_code == 200:
                logger.info("✅ 服务器连接正常")
                return True
            else:
                logger.error(f"❌ 服务器响应异常: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"❌ 无法连接服务器: {e}")
            return False
    
    def test_system_health(self):
        """测试系统健康状态"""
        logger.info("🏥 检查系统健康状态...")
        try:
            response = self.session.get(f"{self.base_url}/system/health", timeout=15)
            if response.status_code == 200:
                health_data = response.json()
                status = health_data.get('status', 'unknown')
                
                logger.info(f"系统状态: {status}")
                
                components = health_data.get('components', {})
                all_healthy = True
                
                for name, comp in components.items():
                    comp_status = comp.get('status', 'unknown')
                    if comp_status == 'healthy':
                        logger.info(f"  ✅ {name}: {comp_status}")
                    elif comp_status == 'not_initialized':
                        logger.warning(f"  ⚠️  {name}: {comp_status}")
                    else:
                        logger.error(f"  ❌ {name}: {comp_status}")
                        if comp.get('error'):
                            logger.error(f"     错误: {comp['error']}")
                        all_healthy = False
                
                return status in ['healthy', 'degraded']
            else:
                logger.error(f"❌ 健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"❌ 健康检查异常: {e}")
            return False
    
    def test_pdf_upload_with_progress(self):
        """测试PDF文件上传和进度监控"""
        if not self.test_pdf_file or not os.path.exists(self.test_pdf_file):
            logger.error("❌ 测试PDF文件不存在")
            return False
        
        logger.info(f"📄 测试PDF文件上传: {self.test_pdf_file}")
        
        try:
            # 1. 上传文件
            with open(self.test_pdf_file, 'rb') as f:
                files = {'file': (self.test_pdf_file, f, 'application/pdf')}
                response = self.session.post(f"{self.base_url}/upload", files=files, timeout=30)
            
            if response.status_code != 200:
                logger.error(f"❌ 上传请求失败: {response.status_code}")
                return False
            
            data = response.json()
            if data.get('status') != 'success':
                logger.error(f"❌ 上传失败: {data.get('message')}")
                return False
            
            upload_id = data.get('upload_id')
            logger.info(f"✅ 文件上传成功，upload_id: {upload_id}")
            
            # 2. 监控处理进度
            return self.monitor_upload_progress(upload_id)
            
        except Exception as e:
            logger.error(f"❌ PDF上传测试失败: {e}")
            return False
    
    def monitor_upload_progress(self, upload_id):
        """监控上传处理进度"""
        logger.info("📊 监控文件处理进度...")
        
        max_wait_time = 300  # 最大等待5分钟
        start_time = time.time()
        last_progress = -1
        
        while time.time() - start_time < max_wait_time:
            try:
                response = self.session.get(f"{self.base_url}/upload/progress/{upload_id}", timeout=10)
                
                if response.status_code != 200:
                    logger.error(f"❌ 进度查询失败: {response.status_code}")
                    return False
                
                data = response.json()
                if data.get('status') != 'success':
                    logger.error(f"❌ 进度查询错误: {data.get('message')}")
                    return False
                
                progress = data.get('progress', {})
                current_progress = progress.get('progress_percentage', 0)
                current_step = progress.get('current_step', '')
                status = progress.get('status', '')
                
                # 显示进度变化
                if abs(current_progress - last_progress) > 5:  # 进度变化超过5%才显示
                    logger.info(f"📈 进度: {current_progress:.1f}% - {current_step}")
                    last_progress = current_progress
                
                # 显示完成的步骤
                steps = progress.get('steps', [])
                for step in steps:
                    if step.get('status') == 'completed' and step.get('duration'):
                        step_name = step['name']
                        duration = step['duration']
                        details = step.get('details', {})
                        
                        if 'extraction_time' in details:
                            logger.info(f"  ✅ {step_name} ({duration:.2f}s) - 提取时间: {details['extraction_time']:.2f}s")
                        elif 'chunks_count' in details:
                            logger.info(f"  ✅ {step_name} ({duration:.2f}s) - 分块数: {details['chunks_count']}")
                        elif 'vectors_count' in details:
                            logger.info(f"  ✅ {step_name} ({duration:.2f}s) - 向量数: {details['vectors_count']}")
                        else:
                            logger.info(f"  ✅ {step_name} ({duration:.2f}s)")
                
                # 检查完成状态
                if status == 'completed':
                    logger.info("🎉 文件处理完成!")
                    self.display_processing_results(progress.get('result', {}))
                    return True
                    
                elif status == 'failed':
                    logger.error(f"❌ 文件处理失败: {progress.get('error', '未知错误')}")
                    return False
                
                # 等待1秒后继续检查
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"❌ 进度监控异常: {e}")
                return False
        
        logger.error("⏰ 文件处理超时")
        return False
    
    def display_processing_results(self, result):
        """显示处理结果详情"""
        logger.info("📋 处理结果详情:")
        logger.info(f"  状态: {result.get('status')}")
        logger.info(f"  处理时间: {result.get('processing_time', 0):.2f}秒")
        logger.info(f"  分块数量: {result.get('chunks_count', 0)}")
        logger.info(f"  文件大小: {result.get('file_size', 0)} 字符")
        
        details = result.get('processing_details', {})
        if details:
            logger.info("  📊 详细信息:")
            logger.info(f"    文件类型: {details.get('file_type', 'Unknown')}")
            logger.info(f"    页面数量: {details.get('pages_count', 0)}")
            logger.info(f"    表格数量: {details.get('tables_count', 0)}")
            logger.info(f"    图片数量: {details.get('images_count', 0)}")
            logger.info(f"    向量维度: {details.get('embedding_dimension', 0)}")
        
        metrics = result.get('performance_metrics', {})
        if metrics:
            logger.info("  ⚡ 性能指标:")
            logger.info(f"    提取时间: {metrics.get('extraction_time', 0):.2f}s")
            logger.info(f"    分块时间: {metrics.get('chunking_time', 0):.2f}s")
            logger.info(f"    嵌入时间: {metrics.get('embedding_time', 0):.2f}s")
            logger.info(f"    存储时间: {metrics.get('storage_time', 0):.2f}s")
            logger.info(f"    处理速度: {metrics.get('chunks_per_second', 0):.1f} chunks/s")
    
    def test_query_functionality(self):
        """测试查询功能"""
        logger.info("🔍 测试查询功能...")
        
        test_queries = [
            "这个文档主要讲什么？",
            "系统有哪些功能特性？",
            "性能指标如何？",
            "技术架构是什么？"
        ]
        
        success_count = 0
        
        for i, query in enumerate(test_queries, 1):
            logger.info(f"📝 测试查询 {i}: {query}")
            
            try:
                query_data = {
                    "query": query,
                    "content_type": "all"
                }
                
                response = self.session.post(
                    f"{self.base_url}/query",
                    json=query_data,
                    headers={'Content-Type': 'application/json'},
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('status') == 'success':
                        answer = data.get('answer', '')
                        latency = data.get('latency', 0)
                        references = data.get('references', [])
                        
                        logger.info(f"  ✅ 查询成功 (响应时间: {latency}s, 参考文档: {len(references)}个)")
                        logger.info(f"  📄 回答预览: {answer[:100]}...")
                        success_count += 1
                    else:
                        logger.warning(f"  ⚠️  查询返回: {data.get('message')}")
                else:
                    logger.error(f"  ❌ 查询请求失败: {response.status_code}")
                    
            except Exception as e:
                logger.error(f"  ❌ 查询异常: {e}")
            
            time.sleep(1)  # 避免请求过快
        
        success_rate = success_count / len(test_queries)
        logger.info(f"🎯 查询测试完成: {success_count}/{len(test_queries)} 成功 ({success_rate*100:.1f}%)")
        
        return success_rate >= 0.75
    
    def cleanup(self):
        """清理测试文件"""
        if self.test_pdf_file and os.path.exists(self.test_pdf_file):
            try:
                os.remove(self.test_pdf_file)
                logger.info(f"🧹 清理测试文件: {self.test_pdf_file}")
            except Exception as e:
                logger.warning(f"清理文件失败: {e}")
    
    def run_complete_test(self):
        """运行完整测试"""
        logger.info("🚀 开始完整系统测试")
        logger.info("=" * 60)
        
        tests = [
            ("生成测试PDF", self.generate_test_pdf),
            ("服务器连接", self.test_server_connection),
            ("系统健康检查", self.test_system_health),
            ("PDF上传处理", self.test_pdf_upload_with_progress),
            ("查询功能", self.test_query_functionality),
        ]
        
        passed = 0
        total = len(tests)
        
        try:
            for test_name, test_func in tests:
                logger.info(f"\n{'='*20} {test_name} {'='*20}")
                try:
                    if test_func():
                        logger.info(f"✅ {test_name} 测试通过")
                        passed += 1
                    else:
                        logger.error(f"❌ {test_name} 测试失败")
                except Exception as e:
                    logger.error(f"❌ {test_name} 测试异常: {e}")
            
            logger.info("\n" + "=" * 60)
            logger.info("🏁 测试结果汇总")
            logger.info("=" * 60)
            logger.info(f"总测试数: {total}")
            logger.info(f"通过测试: {passed}")
            logger.info(f"失败测试: {total - passed}")
            logger.info(f"通过率: {passed/total*100:.1f}%")
            
            if passed == total:
                logger.info("🎉 所有测试通过！系统运行完美")
                return True
            elif passed >= total * 0.8:
                logger.warning("⚠️  大部分测试通过，系统基本正常")
                return True
            else:
                logger.error("❌ 多项测试失败，请检查系统配置")
                return False
                
        finally:
            self.cleanup()

def main():
    """主函数"""
    tester = CompleteSystemTester()
    success = tester.run_complete_test()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
