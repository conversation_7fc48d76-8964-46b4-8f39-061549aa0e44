#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成测试PDF文件
包含文本、表格、图片等多种内容类型
"""

import os
import sys
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.platypus.flowables import PageBreak
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_JUSTIFY
from reportlab.pdfgen import canvas
from reportlab.lib.utils import ImageReader
from PIL import Image as PILImage, ImageDraw, ImageFont
import matplotlib.pyplot as plt
import numpy as np
import io
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_images():
    """创建测试图片"""
    images = []
    
    # 图片1：包含中英文文字的图片
    img1 = PILImage.new('RGB', (400, 200), color='white')
    draw1 = ImageDraw.Draw(img1)
    
    try:
        # 尝试使用系统字体
        font_large = ImageFont.truetype("arial.ttf", 24)
        font_small = ImageFont.truetype("arial.ttf", 16)
    except:
        # 如果没有找到字体，使用默认字体
        font_large = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    draw1.text((20, 20), "智能文档问答系统", fill='black', font=font_large)
    draw1.text((20, 60), "Intelligent Document QA System", fill='blue', font=font_small)
    draw1.text((20, 100), "版本: 2.0", fill='red', font=font_small)
    draw1.text((20, 130), "状态: 测试中", fill='green', font=font_small)
    draw1.text((20, 160), "Date: 2025-01-30", fill='purple', font=font_small)
    
    # 保存图片1
    img1_path = "test_image_1.png"
    img1.save(img1_path)
    images.append(img1_path)
    
    # 图片2：包含数字和表格信息的图片
    img2 = PILImage.new('RGB', (500, 300), color='lightgray')
    draw2 = ImageDraw.Draw(img2)
    
    draw2.text((20, 20), "性能统计图表", fill='black', font=font_large)
    draw2.text((20, 60), "处理速度: 15.6 docs/sec", fill='black', font=font_small)
    draw2.text((20, 90), "准确率: 95.8%", fill='black', font=font_small)
    draw2.text((20, 120), "响应时间: 1.2s", fill='black', font=font_small)
    draw2.text((20, 150), "用户满意度: 4.7/5.0", fill='black', font=font_small)
    
    # 绘制简单的条形图
    draw2.rectangle([300, 60, 450, 80], fill='blue')
    draw2.rectangle([300, 90, 430, 110], fill='green')
    draw2.rectangle([300, 120, 320, 140], fill='orange')
    draw2.rectangle([300, 150, 440, 170], fill='red')
    
    img2_path = "test_image_2.png"
    img2.save(img2_path)
    images.append(img2_path)
    
    # 图片3：使用matplotlib创建图表
    plt.figure(figsize=(8, 6))
    
    # 创建数据
    categories = ['文本处理', '表格识别', '图片OCR', '向量检索', '对话生成']
    values = [92, 88, 85, 96, 90]
    
    # 创建柱状图
    bars = plt.bar(categories, values, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'])
    
    # 添加数值标签
    for bar, value in zip(bars, values):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                f'{value}%', ha='center', va='bottom', fontsize=12)
    
    plt.title('系统各模块准确率统计', fontsize=16, fontweight='bold')
    plt.ylabel('准确率 (%)', fontsize=12)
    plt.xlabel('功能模块', fontsize=12)
    plt.ylim(0, 100)
    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()
    
    # 保存图表
    img3_path = "test_chart.png"
    plt.savefig(img3_path, dpi=150, bbox_inches='tight')
    plt.close()
    images.append(img3_path)
    
    return images

def create_test_pdf():
    """创建测试PDF文件"""
    logger.info("开始生成测试PDF文件...")
    
    # 创建测试图片
    test_images = create_test_images()
    
    # 创建PDF文档
    pdf_filename = "test_document_with_images_tables.pdf"
    doc = SimpleDocTemplate(pdf_filename, pagesize=A4)
    
    # 获取样式
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.darkblue
    )
    
    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=12,
        textColor=colors.darkgreen
    )
    
    normal_style = ParagraphStyle(
        'CustomNormal',
        parent=styles['Normal'],
        fontSize=11,
        spaceAfter=12,
        alignment=TA_JUSTIFY
    )
    
    # 构建文档内容
    story = []
    
    # 标题
    story.append(Paragraph("智能文档问答系统测试文档", title_style))
    story.append(Spacer(1, 20))
    
    # 第一章：系统概述
    story.append(Paragraph("第一章 系统概述", heading_style))
    story.append(Paragraph(
        "本文档是智能文档问答系统的综合测试文档，包含了文本、表格、图片等多种内容类型。"
        "系统基于RAG（检索增强生成）技术，能够处理PDF和TXT文件，提供智能问答服务。"
        "主要功能包括：多轮对话支持、语音交互、混合检索算法、实时进度监控等。",
        normal_style
    ))
    story.append(Spacer(1, 12))
    
    # 插入第一张图片
    story.append(Paragraph("系统架构图：", normal_style))
    try:
        img1 = Image(test_images[0], width=4*inch, height=2*inch)
        story.append(img1)
    except Exception as e:
        logger.warning(f"插入图片1失败: {e}")
    story.append(Spacer(1, 12))
    
    # 第二章：功能特性
    story.append(Paragraph("第二章 功能特性", heading_style))
    
    # 功能列表表格
    table_data = [
        ['功能模块', '状态', '完成度', '优先级', '备注'],
        ['文件上传', '✓ 完成', '100%', '高', '支持PDF/TXT'],
        ['文本处理', '✓ 完成', '100%', '高', '智能分块'],
        ['表格识别', '✓ 完成', '95%', '中', 'PyMuPDF'],
        ['图片OCR', '✓ 完成', '90%', '中', 'EasyOCR+Tesseract'],
        ['向量检索', '✓ 完成', '100%', '高', 'BGE-M3+Milvus'],
        ['对话生成', '✓ 完成', '100%', '高', 'LLM集成'],
        ['语音交互', '✓ 完成', '85%', '低', '待优化'],
        ['进度监控', '✓ 完成', '100%', '中', '实时显示']
    ]
    
    table = Table(table_data, colWidths=[1.2*inch, 0.8*inch, 0.8*inch, 0.8*inch, 1.4*inch])
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 10),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -1), 9),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ]))
    
    story.append(table)
    story.append(Spacer(1, 20))
    
    # 插入第二张图片
    story.append(Paragraph("性能统计信息：", normal_style))
    try:
        img2 = Image(test_images[1], width=4*inch, height=2.4*inch)
        story.append(img2)
    except Exception as e:
        logger.warning(f"插入图片2失败: {e}")
    story.append(Spacer(1, 12))
    
    # 第三章：技术架构
    story.append(Paragraph("第三章 技术架构", heading_style))
    story.append(Paragraph(
        "系统采用模块化设计，主要包括以下组件：",
        normal_style
    ))
    
    # 技术栈表格
    tech_data = [
        ['层级', '技术栈', '版本', '用途'],
        ['前端', 'HTML5 + JavaScript', '-', '用户界面'],
        ['前端框架', 'TailwindCSS', '3.x', '样式框架'],
        ['后端', 'Flask + Python', '3.9+', 'Web服务'],
        ['向量数据库', 'Milvus', '2.3+', '向量存储'],
        ['缓存', 'Redis', '6.0+', '会话缓存'],
        ['AI模型', 'BGE-M3', '-', '文本嵌入'],
        ['重排序', 'BGE-Reranker-v2-M3', '-', '结果重排'],
        ['OCR', 'EasyOCR + Tesseract', '-', '图片识别'],
        ['PDF处理', 'PyMuPDF', '-', '文档解析']
    ]
    
    tech_table = Table(tech_data, colWidths=[1.2*inch, 1.8*inch, 1*inch, 1.5*inch])
    tech_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 10),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.lightblue),
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -1), 9),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ]))
    
    story.append(tech_table)
    story.append(PageBreak())
    
    # 第四章：性能指标
    story.append(Paragraph("第四章 性能指标", heading_style))
    story.append(Paragraph(
        "系统在各项性能测试中表现优异，以下是详细的性能数据统计：",
        normal_style
    ))
    
    # 插入图表
    try:
        chart_img = Image(test_images[2], width=6*inch, height=4.5*inch)
        story.append(chart_img)
    except Exception as e:
        logger.warning(f"插入图表失败: {e}")
    story.append(Spacer(1, 12))
    
    # 性能数据表格
    perf_data = [
        ['指标类别', '指标名称', '数值', '单位', '备注'],
        ['处理性能', '文档处理速度', '15.6', 'docs/sec', '平均值'],
        ['处理性能', '文本分块速度', '120', 'chunks/sec', '智能分块'],
        ['处理性能', '向量生成速度', '85', 'vectors/sec', 'BGE-M3'],
        ['检索性能', '查询响应时间', '1.2', 'seconds', '平均值'],
        ['检索性能', '向量检索精度', '96.8', '%', 'top-10'],
        ['检索性能', '混合检索精度', '98.2', '%', '优化后'],
        ['识别准确率', '文本识别', '99.5', '%', 'PDF文本'],
        ['识别准确率', '表格识别', '92.3', '%', 'PyMuPDF'],
        ['识别准确率', '图片OCR', '88.7', '%', 'EasyOCR'],
        ['系统稳定性', '可用性', '99.9', '%', '7x24小时'],
        ['系统稳定性', '并发处理', '50', 'users', '同时在线'],
        ['用户体验', '满意度评分', '4.7', '/5.0', '用户反馈']
    ]
    
    perf_table = Table(perf_data, colWidths=[1.2*inch, 1.5*inch, 0.8*inch, 0.8*inch, 1.2*inch])
    perf_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.darkgreen),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 9),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.lightgreen),
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -1), 8),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ]))
    
    story.append(perf_table)
    story.append(Spacer(1, 20))
    
    # 第五章：测试结论
    story.append(Paragraph("第五章 测试结论", heading_style))
    story.append(Paragraph(
        "经过全面的功能测试和性能评估，智能文档问答系统在各个方面都表现出色：",
        normal_style
    ))
    
    conclusion_points = [
        "✓ 文档处理功能完善，支持多种格式和内容类型",
        "✓ 向量检索精度高，混合检索算法效果显著",
        "✓ 多轮对话体验流畅，上下文理解准确",
        "✓ 实时进度监控提升用户体验",
        "✓ 系统稳定性强，可支持生产环境部署",
        "✓ 图片OCR和表格识别准确率持续优化中"
    ]
    
    for point in conclusion_points:
        story.append(Paragraph(point, normal_style))
    
    story.append(Spacer(1, 20))
    story.append(Paragraph(
        "本系统已准备好投入实际使用，将为用户提供高效、准确的文档问答服务。",
        normal_style
    ))
    
    # 生成PDF
    doc.build(story)
    
    # 清理临时图片文件
    for img_path in test_images:
        try:
            os.remove(img_path)
        except:
            pass
    
    logger.info(f"测试PDF文件已生成: {pdf_filename}")
    return pdf_filename

def main():
    """主函数"""
    try:
        pdf_file = create_test_pdf()
        print(f"✅ 测试PDF文件生成成功: {pdf_file}")
        print("📄 文件包含以下内容:")
        print("   - 多章节文本内容")
        print("   - 功能特性表格")
        print("   - 技术架构表格") 
        print("   - 性能数据表格")
        print("   - 包含中英文的图片")
        print("   - 性能统计图片")
        print("   - matplotlib生成的图表")
        print("\n🚀 您可以使用此文件测试系统的文档处理功能")
        
    except Exception as e:
        logger.error(f"生成PDF失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
